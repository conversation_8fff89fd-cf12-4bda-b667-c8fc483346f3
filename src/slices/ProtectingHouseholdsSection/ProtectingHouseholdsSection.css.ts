import { breakpoints } from "@/styles/constants.css";
import { theme } from "@/styles/themes.css";
import { style } from "@vanilla-extract/css";
import { mode } from "@/styles/functions.css";

export const wrapper = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  padding: "10px",

  "@media": {
    [breakpoints.tablet]: {
      padding: "72px 48px",
    }
  },

  selectors: {
    [mode("commercial")]: {
      backgroundColor: theme.colors.primary.ivory,
    }
  }
});

export const root = style({
  backgroundColor: theme.colors.primary.castletonGreen,
  padding: "20px",
  color: theme.colors.primary.ivory,
  borderRadius: 0,

  "@media": {
    [breakpoints.tablet]: {
      padding: "48px",
      borderRadius: 24,
      margin: "0 auto",
    }
  },

  selectors: {
    [mode("residential")]: {
      backgroundColor: theme.colors.primary.ivory,
      color: theme.colors.primary.castletonGreen,
      borderRadius: 24,
    }
  }
});

export const container = style({});

export const content = style({
  display: "flex",
  flexDirection: "column",
});

export const title = style({
  fontFamily: theme.fonts.primary,
  color: theme.colors.primary.ivory,
  fontWeight: 400,
  fontSize: "52px",
  lineHeight: '95%',
  letterSpacing: "-1.28px",
  marginBottom: 12,

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "64px",
      marginBottom: 18,
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  }
});

export const highlight = style({
  color: theme.colors.primary.asidGreen,
  fontStyle: "italic",
  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
      fontWeight: 500,
    }
  }
});

export const description = style({
  color: theme.colors.primary.ivory,
  fontSize: "18px",
  lineHeight: 1.2,
  marginBottom: 16,
  letterSpacing: "-0.24px",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "24x",
        marginBottom: 28,
    }
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  }
});

export const trustedText = style({
  color: theme.colors.primary.asidGreen,
  fontWeight: 700,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
      fontWeight: 700,
    }
  }
});

export const needExpert = style({
  color: theme.colors.primary.asidGreen,
  fontWeight: 700,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
      fontWeight: 700,
    }
  }
});

export const mobileCallButton = style({
  display: "flex",
  marginBottom: 16,

  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    }
  }
});

export const callButton = style({
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  gap: 8,
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  padding: "12px 24px",
  fontWeight: 500,
  textDecoration: "none",
  width: "100%",
  borderRadius: '100px'
});

export const phoneIcon = style({
  width: 20,
  height: 20,
});

export const imageAndBoroughs = style({
  display: "flex",
  flexDirection: "column",

  "@media": {
    [breakpoints.tablet]: {
      flexDirection: "row",
      gap: 64,
    }
  }
});

export const imageContainer = style({
  position: "relative",
  width: "100%",
  height: 375,
  overflow: "hidden",
  borderRadius: 16,
  marginBottom: 16,

  "@media": {
    [breakpoints.mobile]: {
      fontSize: "16px",
    },
    [breakpoints.tablet]: {
      width: "40%",
      height: 530,
      marginBottom: 0,
      fontSize: "18px",
      borderRadius: 24,
    },
  }
});

export const image = style({
  objectFit: "cover",
});

export const boroughsContainer = style({
  display: "flex",
  flexDirection: "row",
  gap: 24,

  "@media": {
    [breakpoints.tablet]: {
      gap: 40,
    }
  }
});

export const boroughsList = style({
  display: "flex",
  flexDirection: "column",
  gap: 0,
  // flex: 1,
  padding: 0,
  margin: 0,
  listStyle: "none",
  // Remove maxHeight and overflow properties since we're controlling the number of items

  "@media": {
    [breakpoints.tablet]: {
      // Keep responsive styles
    }
  }
});

export const boroughItem = style({
  display: "flex",
  alignItems: "flex-start",
  gap: 12,
  color: theme.colors.primary.ivory,
  fontSize: "18px",
  fontWeight: 500,
  lineHeight: 1.2,
  marginBottom: 8,
  width: "100%",

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  }
});

export const boroughText = style({
  wordBreak: "break-word",
  hyphens: "auto",
});

export const checkIcon = style({
  color: theme.colors.primary.asidGreen,
  width: 20,
  height: 20,
  minWidth: 20,
  marginTop: 2,
  flexShrink: 0,

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.castletonGreen,
    }
  }
});

export const viewAllContainer = style({
  display: "flex",
  justifyContent: "center",
  borderTop: `1px solid ${theme.colors.primary.asidGreen}20`,
  marginTop: 8,

  "@media": {
    [breakpoints.tablet]: {
      display: "none", // Hide on tablet and above
    }
  }
});

export const viewAllLink = style({
  color: theme.colors.primary.castletonGreen,
  textDecoration: "none",
  fontWeight: 700,
  display: "flex",
  alignItems: "center",
  gap:4,
  fontSize: "18px",
  border: "none",
  background: "transparent",
  cursor: "pointer",
  padding: 0,
  position: "relative",
  paddingBottom: "4px",
  lineHeight: 1.2,

  // Add underline
  "::after": {
    content: '""',
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    height: "1px",
    width: 72,
    backgroundColor: "currentColor",
  }
});

export const expanded = style({
  transform: "rotate(180deg)"
});

export const viewAllIcon = style({
  width: "24px",
  height: "24px",
  transition: "transform 0.2s ease",
});
