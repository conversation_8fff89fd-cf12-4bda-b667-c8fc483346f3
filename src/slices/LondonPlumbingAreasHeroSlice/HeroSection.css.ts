import { style } from "@vanilla-extract/css";
import { theme } from "@/styles/themes.css";
import { breakpoints } from "@/styles/constants.css";
import { mode } from "@/styles/functions.css";

export const root = style({
  padding: "10px 16px 40px",
  backgroundColor: theme.colors.primary.castletonGreen,
  color: theme.colors.primary.ivory,

  "@media": {
    [breakpoints.tablet]: {
      padding: "40px 80px",
    },
  },

  selectors: {
    [mode("commercial")]: {
      backgroundColor: theme.colors.primary.ivory,
      color: theme.colors.primary.castletonGreen,
    }
  },
});

export const container = style({
  display: "flex",
  flexDirection: "column",
  maxWidth: "100%",

  "@media": {
    [breakpoints.tablet]: {
      maxWidth: "1200px",
      margin: "0 auto",
    },
  },
});

export const mainContentWrapper = style({
  padding: "0 20px",
   "@media": {
    [breakpoints.tablet]: {
   padding: 0,
    },
  },
})

// Mobile title - visible only on mobile
export const mobileTitle = style({
  fontSize: "44px",
  lineHeight: "95%",
  fontFamily: theme.fonts.primary,
  margin: "0 0 4px",
  fontWeight: "normal",

  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    },
  },
});

export const mobileSubtitle = style({
  fontSize: "44px",
  lineHeight: "95%",
  fontFamily: theme.fonts.primary,
  fontStyle: "italic",
  fontWeight: "bold",
  margin: "0 0 16px",

  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.asidGreen,
    }
  },
});

export const mobileDescription = style({
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "0 0 20px",
  color: "inherit",

  "@media": {
    [breakpoints.tablet]: {
      display: "none",
    },
  },
});

// Desktop title - visible only on desktop
export const desktopTitle = style({
  fontSize: "88px",
  lineHeight: "95%",
  fontFamily: theme.fonts.primary,
  margin: "0 0 8px",
  fontWeight: "normal",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "70px",
    },
    [breakpoints.desktop]: {
      fontSize: "88px",
    },
  },
});

export const desktopSubtitle = style({
  fontSize: "88px",
  lineHeight: "95%",
  fontFamily: theme.fonts.primary,
  fontStyle: "italic",
  fontWeight: "bold",
  margin: "0 0 30px",
  textAlign: "center",

  "@media": {
    [breakpoints.tablet]: {
      fontSize: "70px",
    },
    [breakpoints.desktop]: {
      fontSize: "88px",
    },
  },

  selectors: {
    [mode("residential")]: {
      color: theme.colors.primary.asidGreen,
    }
  },
});

// Desktop content - visible only on desktop
export const desktopContent = style({
  display: "none",

  "@media": {
    [breakpoints.tablet]: {
      display: "block",
      textAlign: "center",
      // maxWidth: "900px",
      margin: "0 auto",
    },
  },
});

export const description = style({
  fontSize: "18px",
  margin: "0 0 24px",
  textAlign: "center",
  fontFeatureSettings: "'liga' off, 'clig' off",
  fontFamily: theme.fonts.secondary,
  lineHeight: "120%",
  "@media": {
    [breakpoints.tablet]: {
      fontSize: "20px",
    }
  },

});

// Search container
export const searchContainer = style({
  padding: '0 20px',
  width: "100%",
  maxWidth: "500px",
  margin: "0 auto 20px auto",

  "@media": {
    [breakpoints.tablet]: {
      padding: 0,
      margin: "24px auto 48px auto",
    }
  },
});

export const searchLabel = style({
  fontSize: "18px",
  fontWeight: "500",
  marginBottom: "16px",
  color: "inherit",
  display: "block",
  textAlign: "left",

  "@media": {
    [breakpoints.desktop]: {
      fontSize: "24px",
    }
  },
});

export const searchInputContainer = style({
  display: "flex",
  width: "100%",
  position: "relative",
  borderRadius: "8px",
  overflow: "hidden",
});

export const searchInput = style({
  flex: "1",
  padding: "15px 20px",
  borderRadius: "8px",
  border: "1px solid #969691",
  fontSize: "16px",
  color: "#888",
  backgroundColor: theme.colors.primary.ivory,
  height: "56px",
});

export const searchButton = style({
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
  border: "none",
  borderRadius: "8px",
  width: "80px",
  height: "56px",
  cursor: "pointer",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
  fontSize: "24px",
  fontWeight: "bold",
  marginLeft: "5px"
});

export const modalContainer = style({
  textAlign: 'center',
  maxWidth: '400px',
});

export const modalMessage = style({
  marginBottom: '20px',
});

export const modalSuccessText = style({
  marginBottom: '30px',
  color: theme.colors.primary.castletonGreen,
});

export const modalButtonsContainer = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '15px',
});

export const successButton = style({
  minWidth: '120px',
  backgroundColor: theme.colors.primary.asidGreen,
  color: theme.colors.primary.castletonGreen,
});

export const closeButton = style({
  minWidth: '120px',
  color: theme.colors.primary.softWhite,
});

export const successCloseButton = style({
  backgroundColor: '#1A3C34', // Darker green
});

export const errorCloseButton = style({
  backgroundColor: '#D32F2F', // Error red
});

