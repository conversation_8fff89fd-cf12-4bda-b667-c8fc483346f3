import { Content } from '@prismicio/client';
import {
  PrismicRichText,
  PrismicText,
  SliceComponentProps,
} from '@prismicio/react';
import * as styles from '@/slices/LondonPlumbingAreasHeroSlice/HeroSection.css';
import InteractiveHero from '@/slices/LondonPlumbingAreasHeroSlice/InteractHero';

/**
 * Props for `LondonPlumbingAreas`.
 */
export type LondonPlumbingAreasProps =
  SliceComponentProps<Content.LondonPlumbingAreasSlice>;

/**
 * Component for "LondonPlumbingAreas" Slices.
 */
const LondonPlumbingAreas = ({
  slice,
}: LondonPlumbingAreasProps): JSX.Element => {
  return (
    <section
      className={styles.root}
      data-slice-type={slice.slice_type}
      data-slice-variation={slice.variation}
    >
      <div className={styles.container}>
        <div className={styles.mainContentWrapper}>
          {/* Mobile title - hidden on desktop */}
          <h1 className={styles.mobileTitle}>
            <PrismicText field={slice.primary.title} />
          </h1>
          <h2 className={styles.mobileSubtitle}>
            <PrismicText field={slice.primary.subtitle} />
          </h2>

          <p className={styles.mobileDescription}>
            <PrismicText field={slice.primary.mobileDescription} />
          </p>

          {/* Desktop content - hidden on mobile */}
          <div className={styles.desktopContent}>
            <h1 className={styles.desktopTitle}>
              <PrismicRichText field={slice.primary.title} />
            </h1>
            <h2 className={styles.desktopSubtitle}>
              <PrismicRichText field={slice.primary.subtitle} />
            </h2>
            {slice?.items.map((item, i) => (
              <p key={i} className={styles.description}>
                <PrismicRichText field={item.pargraph} />
              </p>
            ))}
          </div>
        </div>

        {/* All interactive elements are now in the client component */}
        <InteractiveHero slice={slice} />
      </div>
    </section>
  );
};

export default LondonPlumbingAreas;
